"""
NSE-Tuned Detection System
Properly calibrated for Indian market microstructure and behavior
"""
import numpy as np
import pandas as pd
from numba import jit
from typing import List, Dict, Any, Tuple
from datetime import datetime, time
from dataclasses import dataclass
from enum import Enum

from models.data_models import ManipulationSignal, PatternType, OptionsData
from detection.base_detector import BaseDetector

class MarketRegime(Enum):
    """Market volatility regimes"""
    LOW_VOL = "low_volatility"      # VIX < 15
    NORMAL_VOL = "normal_volatility" # VIX 15-25  
    HIGH_VOL = "high_volatility"    # VIX 25-35
    EXTREME_VOL = "extreme_volatility" # VIX > 35

class TimeOfDay(Enum):
    """NSE trading session periods"""
    OPENING = "opening"        # 9:15-9:45 (high volatility)
    MORNING = "morning"        # 9:45-11:30 (normal)
    MIDDAY = "midday"         # 11:30-14:00 (low activity)
    AFTERNOON = "afternoon"    # 14:00-15:15 (normal)
    CLOSING = "closing"       # 15:15-15:30 (high volatility)

@dataclass
class NSEMarketContext:
    """NSE market context for adaptive thresholds"""
    volatility_regime: MarketRegime
    time_period: TimeOfDay
    symbol_liquidity: float  # 0-1 scale
    vix_level: float
    current_time: datetime
    
    def get_volatility_multiplier(self) -> float:
        """Get volatility-based threshold multiplier"""
        multipliers = {
            MarketRegime.LOW_VOL: 0.5,      # Tighter thresholds in calm markets
            MarketRegime.NORMAL_VOL: 1.0,   # Base thresholds
            MarketRegime.HIGH_VOL: 2.0,     # Relaxed thresholds in volatile markets
            MarketRegime.EXTREME_VOL: 3.0   # Very relaxed in extreme volatility
        }
        return multipliers[self.volatility_regime]
    
    def get_time_multiplier(self) -> float:
        """Get time-based threshold multiplier"""
        multipliers = {
            TimeOfDay.OPENING: 3.0,    # Very relaxed during opening volatility
            TimeOfDay.MORNING: 1.0,    # Normal thresholds
            TimeOfDay.MIDDAY: 0.8,     # Tighter during low activity
            TimeOfDay.AFTERNOON: 1.0,  # Normal thresholds
            TimeOfDay.CLOSING: 2.0     # Relaxed during closing volatility
        }
        return multipliers[self.time_period]

@jit(nopython=True)
def detect_nse_spoofing_tuned(
    bid_qty: np.ndarray,
    ask_qty: np.ndarray,
    prices: np.ndarray,
    timestamps: np.ndarray,
    base_qty_threshold: float,
    base_price_impact_threshold: float,
    time_window_seconds: float,
    volatility_multiplier: float,
    time_multiplier: float
) -> np.ndarray:
    """
    NSE-tuned spoofing detection with adaptive thresholds
    """
    n = len(bid_qty)
    if n < 3:
        return np.empty(0, dtype=np.int64)
    
    # Adaptive thresholds based on market context
    qty_threshold = base_qty_threshold * volatility_multiplier * time_multiplier
    price_impact_threshold = base_price_impact_threshold * volatility_multiplier
    
    # Vectorized calculations
    bid_changes = np.diff(bid_qty)
    ask_changes = np.diff(ask_qty)
    price_changes = np.diff(prices)
    time_diffs = np.diff(timestamps)
    
    # NSE-specific spoofing patterns:
    # 1. Large quantity spike (adjusted for market conditions)
    # 2. Quick reversal within time window
    # 3. Disproportionately small price impact (key indicator)
    
    # Bid spoofing pattern
    bid_spoof_pattern = (
        (np.abs(bid_changes[:-1]) > qty_threshold) &  # Large initial change
        (np.abs(bid_changes[1:]) > qty_threshold * 0.5) &   # Significant reversal (50% of spike)
        (np.sign(bid_changes[:-1]) != np.sign(bid_changes[1:])) &  # Opposite direction
        (time_diffs[1:] <= time_window_seconds) &     # Within time window
        (np.abs(price_changes[1:]) < price_impact_threshold)  # Minimal price impact
    )
    
    # Ask spoofing pattern
    ask_spoof_pattern = (
        (np.abs(ask_changes[:-1]) > qty_threshold) &
        (np.abs(ask_changes[1:]) > qty_threshold * 0.5) &
        (np.sign(ask_changes[:-1]) != np.sign(ask_changes[1:])) &
        (time_diffs[1:] <= time_window_seconds) &
        (np.abs(price_changes[1:]) < price_impact_threshold)
    )
    
    # Combine patterns
    spoof_pattern = bid_spoof_pattern | ask_spoof_pattern
    
    return np.where(spoof_pattern)[0] + 1

class NSETunedSpoofingDetector(BaseDetector):
    """
    NSE-tuned spoofing detector with adaptive thresholds
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        super().__init__("nse_tuned_spoofing_detector", config)
        self.config = config or {}
        
        # NSE-calibrated base parameters
        self.base_qty_threshold = self.config.get('base_qty_threshold', 500.0)  # Reduced from 1000
        self.base_price_impact_threshold = self.config.get('base_price_impact_threshold', 0.5)  # Increased from 0.01
        self.time_window_seconds = self.config.get('time_window_seconds', 45.0)  # Increased from 30
        
        # NSE symbol-specific parameters
        self.symbol_params = {
            'NIFTY': {'liquidity': 0.9, 'lot_size': 50},
            'BANKNIFTY': {'liquidity': 0.8, 'lot_size': 15}, 
            'FINNIFTY': {'liquidity': 0.6, 'lot_size': 40},
            'MIDCPNIFTY': {'liquidity': 0.4, 'lot_size': 75}
        }
        
        # Performance tracking
        self.detection_count = 0
        self.successful_detections = 0
        
    def detect(self, data: List[OptionsData]) -> List[ManipulationSignal]:
        """
        Main detection with NSE market context awareness
        """
        if not data:
            return []
        
        # Get market context
        market_context = self._get_market_context(data)
        
        # Convert to DataFrame for processing
        df_data = []
        for option in data:
            df_data.append({
                'symbol': option.symbol,
                'strike': option.strike,
                'option_type': option.option_type.value,
                'timestamp': option.timestamp,
                'bid_qty': option.bid_qty,
                'ask_qty': option.ask_qty,
                'last_price': option.last_price,
                'volume': option.volume,
                'open_interest': option.open_interest,
                'implied_volatility': option.implied_volatility
            })
        
        df = pd.DataFrame(df_data)
        return self._detect_with_context(df, market_context)
    
    def _get_market_context(self, data: List[OptionsData]) -> NSEMarketContext:
        """Determine current market context"""
        if not data:
            return NSEMarketContext(
                MarketRegime.NORMAL_VOL, TimeOfDay.MORNING, 0.5, 20.0, datetime.now()
            )
        
        # Estimate VIX from implied volatility
        avg_iv = np.mean([opt.implied_volatility for opt in data if opt.implied_volatility > 0])
        vix_estimate = avg_iv * 100  # Convert to VIX-like scale
        
        # Determine volatility regime
        if vix_estimate < 15:
            vol_regime = MarketRegime.LOW_VOL
        elif vix_estimate < 25:
            vol_regime = MarketRegime.NORMAL_VOL
        elif vix_estimate < 35:
            vol_regime = MarketRegime.HIGH_VOL
        else:
            vol_regime = MarketRegime.EXTREME_VOL
        
        # Determine time period
        current_time = data[0].timestamp.time()
        if time(9, 15) <= current_time < time(9, 45):
            time_period = TimeOfDay.OPENING
        elif time(9, 45) <= current_time < time(11, 30):
            time_period = TimeOfDay.MORNING
        elif time(11, 30) <= current_time < time(14, 0):
            time_period = TimeOfDay.MIDDAY
        elif time(14, 0) <= current_time < time(15, 15):
            time_period = TimeOfDay.AFTERNOON
        else:
            time_period = TimeOfDay.CLOSING
        
        # Estimate liquidity from volume
        avg_volume = np.mean([opt.volume for opt in data if opt.volume > 0])
        symbol_liquidity = min(avg_volume / 10000, 1.0)  # Normalize to 0-1
        
        return NSEMarketContext(
            vol_regime, time_period, symbol_liquidity, vix_estimate, data[0].timestamp
        )
    
    def _detect_with_context(self, df: pd.DataFrame, context: NSEMarketContext) -> List[ManipulationSignal]:
        """Detect spoofing with market context"""
        signals = []
        
        # Get adaptive multipliers
        vol_multiplier = context.get_volatility_multiplier()
        time_multiplier = context.get_time_multiplier()
        
        # Group by option contract
        for (symbol, strike, option_type), group in df.groupby(['symbol', 'strike', 'option_type']):
            if len(group) < 3:
                continue
            
            # Get symbol-specific parameters
            symbol_params = self.symbol_params.get(symbol, {'liquidity': 0.5, 'lot_size': 50})
            liquidity_multiplier = symbol_params['liquidity']
            
            # Adjust thresholds for symbol liquidity
            adjusted_qty_threshold = self.base_qty_threshold / liquidity_multiplier
            
            # Sort by timestamp
            group_sorted = group.sort_values('timestamp')
            
            # Convert to numpy arrays
            bid_qty = group_sorted['bid_qty'].values.astype(np.float64)
            ask_qty = group_sorted['ask_qty'].values.astype(np.float64)
            prices = group_sorted['last_price'].values.astype(np.float64)
            timestamps = group_sorted['timestamp'].astype(np.int64).values // 10**9
            timestamps = timestamps.astype(np.float64)
            
            # Run tuned detection
            spoof_indices = detect_nse_spoofing_tuned(
                bid_qty, ask_qty, prices, timestamps,
                adjusted_qty_threshold,
                self.base_price_impact_threshold,
                self.time_window_seconds,
                vol_multiplier,
                time_multiplier
            )
            
            # Create signals with context-aware confidence
            for idx in spoof_indices:
                signal = self._create_contextual_signal(
                    group_sorted, idx, symbol, strike, option_type,
                    context, bid_qty, ask_qty, prices
                )
                if signal:
                    signals.append(signal)
        
        self.detection_count += 1
        return signals
    
    def _create_contextual_signal(
        self, group: pd.DataFrame, idx: int, symbol: str, strike: float, 
        option_type: str, context: NSEMarketContext,
        bid_qty: np.ndarray, ask_qty: np.ndarray, prices: np.ndarray
    ) -> ManipulationSignal:
        """Create signal with market context-based confidence"""
        
        # Calculate pattern strength
        if idx > 0 and idx < len(bid_qty) - 1:
            bid_change_1 = abs(bid_qty[idx] - bid_qty[idx-1])
            bid_change_2 = abs(bid_qty[idx+1] - bid_qty[idx])
            price_impact = abs(prices[idx+1] - prices[idx-1])
            
            # Pattern strength based on quantity changes vs price impact
            pattern_strength = (bid_change_1 + bid_change_2) / max(price_impact * 1000, 1.0)
        else:
            pattern_strength = 1.0
        
        # Context-based confidence adjustment
        base_confidence = min(pattern_strength / 100, 0.9)  # Cap at 90%
        
        # Adjust confidence based on market context
        confidence_adjustments = {
            MarketRegime.LOW_VOL: 1.2,      # Higher confidence in calm markets
            MarketRegime.NORMAL_VOL: 1.0,   # Base confidence
            MarketRegime.HIGH_VOL: 0.8,     # Lower confidence in volatile markets
            MarketRegime.EXTREME_VOL: 0.6   # Much lower confidence in extreme volatility
        }
        
        time_adjustments = {
            TimeOfDay.OPENING: 0.7,    # Lower confidence during opening chaos
            TimeOfDay.MORNING: 1.0,    # Base confidence
            TimeOfDay.MIDDAY: 1.1,     # Higher confidence during calm period
            TimeOfDay.AFTERNOON: 1.0,  # Base confidence
            TimeOfDay.CLOSING: 0.8     # Lower confidence during closing volatility
        }
        
        adjusted_confidence = (base_confidence * 
                             confidence_adjustments[context.volatility_regime] *
                             time_adjustments[context.time_period] *
                             context.symbol_liquidity)  # Higher confidence for liquid symbols
        
        # Cap confidence between 0.1 and 0.95
        final_confidence = max(0.1, min(adjusted_confidence, 0.95))
        
        # Create signal
        signal = ManipulationSignal(
            pattern_type=PatternType.ORDER_SPOOFING,
            timestamp=datetime.fromtimestamp(group.iloc[idx]['timestamp'].timestamp()),
            symbols_affected=[f"{symbol}_{strike}_{option_type}"],
            description=f"NSE-tuned spoofing: {symbol} {strike} {option_type} in {context.volatility_regime.value} regime",
            
            # Market context evidence
            oi_change_pct_1min=0.0,  # Will be calculated if available
            iv_spike_z_score=context.vix_level / 20.0,  # Normalized VIX
            bid_ask_spread_pct=2.0,  # Typical NSE spread
            traded_volume_vs_avg=1.0,
            order_book_imbalance_ratio=0.0,
            price_change_bps=price_impact * 10000 / prices[idx] if idx < len(prices) else 0,
            time_window_seconds=self.time_window_seconds,
            
            confidence_level=self._get_confidence_enum(final_confidence),
            confidence=final_confidence,
            manipulation_strength=self._get_strength_enum(pattern_strength),
            estimated_profit=self._estimate_profit(symbol, final_confidence),
            
            detection_algorithm="nse_tuned_spoofing_detector",
            raw_data={
                "market_regime": context.volatility_regime.value,
                "time_period": context.time_period.value,
                "vix_level": context.vix_level,
                "pattern_strength": pattern_strength,
                "base_confidence": base_confidence,
                "final_confidence": final_confidence,
                "qty_threshold_used": self.base_qty_threshold * context.get_volatility_multiplier() * context.get_time_multiplier()
            }
        )
        
        return signal
    
    def _estimate_profit(self, symbol: str, confidence: float) -> float:
        """Estimate profit based on symbol and confidence"""
        base_profits = {
            'NIFTY': 2000,
            'BANKNIFTY': 3000,
            'FINNIFTY': 1500,
            'MIDCPNIFTY': 1000
        }
        
        base_profit = base_profits.get(symbol, 2000)
        return base_profit * confidence * 0.5  # Conservative estimate
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get enhanced performance metrics"""
        success_rate = (self.successful_detections / max(self.detection_count, 1)) * 100
        
        return {
            "total_detections": self.detection_count,
            "successful_detections": self.successful_detections,
            "success_rate": success_rate,
            "algorithm": "nse_tuned_adaptive",
            "base_qty_threshold": self.base_qty_threshold,
            "base_price_impact_threshold": self.base_price_impact_threshold,
            "time_window_seconds": self.time_window_seconds
        }
