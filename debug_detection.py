#!/usr/bin/env python3
"""
DEBUG: Detection Algorithm Analysis
Analyze why the detection algorithms are not finding patterns
"""
import asyncio
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Any
import logging

# Import your system components
from models.data_models import OptionsData, OptionType
from detection.vectorized_spoofing_detector import VectorizedSpoofingDetector

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_obvious_spoofing_pattern() -> List[OptionsData]:
    """Create extremely obvious spoofing pattern"""
    options_data = []
    base_time = datetime.now()
    
    # Create time series with MASSIVE spoofing pattern
    patterns = [
        # Normal trading
        {"bid_qty": 100, "ask_qty": 120, "price": 100.0},
        {"bid_qty": 110, "ask_qty": 130, "price": 100.5},
        {"bid_qty": 90, "ask_qty": 110, "price": 99.8},
        
        # MASSIVE BID SPOOF
        {"bid_qty": 10000, "ask_qty": 120, "price": 101.0},  # Huge bid appears
        {"bid_qty": 15000, "ask_qty": 130, "price": 101.2},  # Even bigger bid
        
        # SPOOF REMOVED - price barely moves (key indicator)
        {"bid_qty": 100, "ask_qty": 120, "price": 100.8},   # Spoof gone, price drops
        {"bid_qty": 95, "ask_qty": 115, "price": 100.5},    # Back to normal
    ]
    
    for i, pattern in enumerate(patterns):
        timestamp = base_time + timedelta(seconds=i*10)  # 10 second intervals
        
        option = OptionsData(
            symbol="NIFTY",
            strike=25000.0,
            option_type=OptionType.CALL,
            expiry_date=datetime.now() + timedelta(days=7),
            timestamp=timestamp,
            last_price=pattern["price"],
            bid_price=pattern["price"] * 0.98,
            ask_price=pattern["price"] * 1.02,
            bid_qty=pattern["bid_qty"],
            ask_qty=pattern["ask_qty"],
            volume=1000,
            open_interest=10000,
            implied_volatility=20.0,
            delta=0.5,
            gamma=0.01,
            theta=-0.05,
            vega=0.15
        )
        options_data.append(option)
    
    return options_data

def analyze_detection_parameters():
    """Analyze detection parameters and thresholds"""
    detector = VectorizedSpoofingDetector()
    
    logger.info("🔍 DETECTION PARAMETERS ANALYSIS")
    logger.info("=" * 50)
    logger.info(f"Quantity threshold: {detector.qty_threshold}")
    logger.info(f"Time window: {detector.time_window_seconds} seconds")
    logger.info(f"Min price impact: {detector.min_price_impact}")
    
    # Create test data
    test_data = create_obvious_spoofing_pattern()
    
    logger.info(f"\n📊 TEST DATA ANALYSIS")
    logger.info("=" * 50)
    
    for i, option in enumerate(test_data):
        logger.info(f"Point {i}: Bid={option.bid_qty:,}, Ask={option.ask_qty:,}, Price=₹{option.last_price}")
    
    # Convert to DataFrame for manual analysis
    df_data = []
    for option in test_data:
        df_data.append({
            'timestamp': option.timestamp,
            'bid_qty': option.bid_qty,
            'ask_qty': option.ask_qty,
            'last_price': option.last_price
        })
    
    df = pd.DataFrame(df_data)
    df = df.sort_values('timestamp')
    
    # Manual spoofing analysis
    logger.info(f"\n🔍 MANUAL SPOOFING ANALYSIS")
    logger.info("=" * 50)
    
    bid_changes = df['bid_qty'].diff()
    price_changes = df['last_price'].diff()
    
    logger.info("Bid quantity changes:")
    for i, change in enumerate(bid_changes):
        if pd.notna(change):
            logger.info(f"  Point {i}: {change:+,.0f} (threshold: {detector.qty_threshold:,.0f})")
    
    logger.info("\nPrice changes:")
    for i, change in enumerate(price_changes):
        if pd.notna(change):
            logger.info(f"  Point {i}: ₹{change:+.2f} (min impact: {detector.min_price_impact})")
    
    # Check for spoofing pattern manually
    logger.info(f"\n🎯 SPOOFING PATTERN CHECK")
    logger.info("=" * 50)
    
    for i in range(1, len(bid_changes)-1):
        if pd.notna(bid_changes.iloc[i]) and pd.notna(bid_changes.iloc[i+1]):
            change1 = bid_changes.iloc[i]
            change2 = bid_changes.iloc[i+1]
            price_impact = abs(price_changes.iloc[i+1]) if pd.notna(price_changes.iloc[i+1]) else 0
            
            # Check spoofing conditions
            large_spike = abs(change1) > detector.qty_threshold
            large_reversal = abs(change2) > detector.qty_threshold
            opposite_direction = np.sign(change1) != np.sign(change2)
            minimal_impact = price_impact < detector.min_price_impact
            
            logger.info(f"Point {i}-{i+1}:")
            logger.info(f"  Large spike ({abs(change1):,.0f} > {detector.qty_threshold:,.0f}): {large_spike}")
            logger.info(f"  Large reversal ({abs(change2):,.0f} > {detector.qty_threshold:,.0f}): {large_reversal}")
            logger.info(f"  Opposite direction: {opposite_direction}")
            logger.info(f"  Minimal price impact ({price_impact:.3f} < {detector.min_price_impact}): {minimal_impact}")
            
            if large_spike and large_reversal and opposite_direction and minimal_impact:
                logger.info(f"  ✅ SPOOFING PATTERN DETECTED!")
            else:
                logger.info(f"  ❌ No spoofing pattern")
    
    # Run actual detector
    logger.info(f"\n🤖 ACTUAL DETECTOR RESULTS")
    logger.info("=" * 50)
    
    signals = detector.detect(test_data)
    logger.info(f"Signals detected: {len(signals)}")
    
    for signal in signals:
        logger.info(f"✅ Signal: {signal.pattern_type.value}")
        logger.info(f"   Confidence: {signal.confidence:.1%}")
        logger.info(f"   Description: {signal.description}")

def test_with_different_thresholds():
    """Test detection with different parameter thresholds"""
    logger.info(f"\n🔧 THRESHOLD SENSITIVITY ANALYSIS")
    logger.info("=" * 50)
    
    test_data = create_obvious_spoofing_pattern()
    
    # Test different quantity thresholds
    qty_thresholds = [100, 500, 1000, 2000, 5000]
    
    for threshold in qty_thresholds:
        detector = VectorizedSpoofingDetector({'qty_threshold': threshold})
        signals = detector.detect(test_data)
        logger.info(f"Qty threshold {threshold:,}: {len(signals)} signals")
    
    # Test different time windows
    time_windows = [10, 30, 60, 120]
    
    for window in time_windows:
        detector = VectorizedSpoofingDetector({'time_window_seconds': window})
        signals = detector.detect(test_data)
        logger.info(f"Time window {window}s: {len(signals)} signals")

async def main():
    """Run detection debugging"""
    logger.info("🔥 DETECTION ALGORITHM DEBUG")
    logger.info("=" * 80)
    
    # Analyze current parameters
    analyze_detection_parameters()
    
    # Test different thresholds
    test_with_different_thresholds()
    
    logger.info("\n" + "🔥" * 50)
    logger.info("DEBUG COMPLETE")
    logger.info("🔥" * 50)

if __name__ == "__main__":
    asyncio.run(main())
