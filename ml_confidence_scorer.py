"""
ML-Enhanced Signal Confidence Scoring
Uses machine learning to predict signal success probability based on market context
"""
import numpy as np
import pandas as pd
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass
from datetime import datetime, timedelta
import pickle
import logging
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, roc_auc_score

from models.data_models import ManipulationSignal, OptionsData

logger = logging.getLogger(__name__)

@dataclass
class SignalFeatures:
    """Features extracted from signal and market context"""
    # Pattern features
    pattern_strength: float
    quantity_ratio: float
    price_impact_ratio: float
    time_window_ratio: float
    
    # Market context features
    volatility_percentile: float
    volume_percentile: float
    time_of_day_score: float
    liquidity_score: float
    
    # Historical features
    recent_success_rate: float
    symbol_success_rate: float
    pattern_frequency: float
    
    # Technical features
    rsi_level: float
    bollinger_position: float
    volume_ma_ratio: float

class MLConfidenceScorer:
    """
    Machine learning model for signal confidence scoring
    Predicts probability of signal success based on market context
    """
    
    def __init__(self, model_path: Optional[str] = None):
        self.model = None
        self.scaler = StandardScaler()
        self.feature_importance = {}
        self.is_trained = False
        
        # Historical performance tracking
        self.signal_history = []
        self.performance_window = 1000  # Keep last 1000 signals
        
        # Model parameters
        self.model_params = {
            'n_estimators': 100,
            'max_depth': 10,
            'min_samples_split': 20,
            'min_samples_leaf': 10,
            'random_state': 42
        }
        
        if model_path:
            self.load_model(model_path)
        else:
            self._initialize_model()
    
    def _initialize_model(self):
        """Initialize the ML model"""
        self.model = GradientBoostingClassifier(**self.model_params)
        logger.info("Initialized ML confidence scoring model")
    
    def extract_features(self, signal: ManipulationSignal, market_data: List[OptionsData], 
                        historical_context: Dict[str, Any] = None) -> SignalFeatures:
        """Extract features from signal and market context"""
        
        # Get signal-specific data
        signal_option = self._find_signal_option(signal, market_data)
        if not signal_option:
            return self._get_default_features()
        
        # Pattern strength features
        pattern_strength = getattr(signal, 'confidence', 0.5)
        
        # Calculate quantity and price ratios from raw data
        raw_data = getattr(signal, 'raw_data', {})
        quantity_ratio = self._calculate_quantity_ratio(raw_data)
        price_impact_ratio = self._calculate_price_impact_ratio(raw_data)
        time_window_ratio = raw_data.get('time_window_seconds', 30) / 60.0  # Normalize to minutes
        
        # Market context features
        volatility_percentile = self._calculate_volatility_percentile(signal_option, market_data)
        volume_percentile = self._calculate_volume_percentile(signal_option, market_data)
        time_of_day_score = self._calculate_time_score(signal.timestamp)
        liquidity_score = self._calculate_liquidity_score(signal_option)
        
        # Historical context features
        hist_context = historical_context or {}
        recent_success_rate = hist_context.get('recent_success_rate', 0.5)
        symbol_success_rate = hist_context.get('symbol_success_rate', 0.5)
        pattern_frequency = hist_context.get('pattern_frequency', 0.1)
        
        # Technical indicators
        rsi_level = self._calculate_rsi(signal_option, market_data)
        bollinger_position = self._calculate_bollinger_position(signal_option, market_data)
        volume_ma_ratio = self._calculate_volume_ma_ratio(signal_option, market_data)
        
        return SignalFeatures(
            pattern_strength=pattern_strength,
            quantity_ratio=quantity_ratio,
            price_impact_ratio=price_impact_ratio,
            time_window_ratio=time_window_ratio,
            volatility_percentile=volatility_percentile,
            volume_percentile=volume_percentile,
            time_of_day_score=time_of_day_score,
            liquidity_score=liquidity_score,
            recent_success_rate=recent_success_rate,
            symbol_success_rate=symbol_success_rate,
            pattern_frequency=pattern_frequency,
            rsi_level=rsi_level,
            bollinger_position=bollinger_position,
            volume_ma_ratio=volume_ma_ratio
        )
    
    def score_signal_confidence(self, signal: ManipulationSignal, market_data: List[OptionsData],
                               historical_context: Dict[str, Any] = None) -> float:
        """Score signal confidence using ML model"""
        
        if not self.is_trained:
            # Fallback to rule-based scoring if model not trained
            return self._rule_based_confidence(signal, market_data)
        
        # Extract features
        features = self.extract_features(signal, market_data, historical_context)
        feature_vector = self._features_to_vector(features)
        
        # Scale features
        feature_vector_scaled = self.scaler.transform([feature_vector])
        
        # Predict probability
        try:
            confidence_prob = self.model.predict_proba(feature_vector_scaled)[0][1]  # Probability of success
            
            # Ensure confidence is in reasonable range
            confidence_prob = max(0.1, min(confidence_prob, 0.95))
            
            logger.debug(f"ML confidence score: {confidence_prob:.3f} for {signal.pattern_type.value}")
            return confidence_prob
            
        except Exception as e:
            logger.error(f"Error in ML confidence scoring: {e}")
            return self._rule_based_confidence(signal, market_data)
    
    def train_model(self, training_data: List[Dict[str, Any]]) -> Dict[str, float]:
        """Train the ML model on historical signal data"""
        
        if len(training_data) < 50:
            logger.warning("Insufficient training data for ML model")
            return {"error": "insufficient_data"}
        
        # Prepare training data
        X, y = self._prepare_training_data(training_data)
        
        if len(X) == 0:
            logger.error("No valid training samples")
            return {"error": "no_valid_samples"}
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        # Scale features
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)
        
        # Train model
        self.model.fit(X_train_scaled, y_train)
        self.is_trained = True
        
        # Evaluate model
        train_score = self.model.score(X_train_scaled, y_train)
        test_score = self.model.score(X_test_scaled, y_test)
        
        # Get feature importance
        feature_names = self._get_feature_names()
        self.feature_importance = dict(zip(feature_names, self.model.feature_importances_))
        
        # Calculate AUC
        y_pred_proba = self.model.predict_proba(X_test_scaled)[:, 1]
        auc_score = roc_auc_score(y_test, y_pred_proba)
        
        logger.info(f"ML model trained: Train={train_score:.3f}, Test={test_score:.3f}, AUC={auc_score:.3f}")
        
        return {
            "train_accuracy": train_score,
            "test_accuracy": test_score,
            "auc_score": auc_score,
            "training_samples": len(X_train),
            "test_samples": len(X_test),
            "feature_importance": self.feature_importance
        }
    
    def update_signal_outcome(self, signal_id: str, was_successful: bool, profit_pct: float):
        """Update model with signal outcome for online learning"""
        
        # Store outcome
        outcome = {
            'signal_id': signal_id,
            'was_successful': was_successful,
            'profit_pct': profit_pct,
            'timestamp': datetime.now()
        }
        
        self.signal_history.append(outcome)
        
        # Keep only recent history
        if len(self.signal_history) > self.performance_window:
            self.signal_history = self.signal_history[-self.performance_window:]
        
        # Retrain model periodically
        if len(self.signal_history) % 100 == 0 and len(self.signal_history) >= 200:
            logger.info("Retraining ML model with recent outcomes")
            self._retrain_with_recent_data()
    
    def _rule_based_confidence(self, signal: ManipulationSignal, market_data: List[OptionsData]) -> float:
        """Fallback rule-based confidence scoring"""
        
        base_confidence = getattr(signal, 'confidence', 0.5)
        
        # Adjust based on pattern type
        pattern_multipliers = {
            'ORDER_SPOOFING': 0.8,      # Lower confidence for spoofing
            'INSTITUTIONAL_FLOW': 1.2,   # Higher confidence for flow
            'EXPIRY_MANIPULATION': 1.1,  # Good confidence for expiry
            'GAMMA_SQUEEZE_SETUP': 0.9   # Medium confidence for gamma
        }
        
        pattern_type = signal.pattern_type.value
        multiplier = pattern_multipliers.get(pattern_type, 1.0)
        
        # Adjust based on time of day
        time_score = self._calculate_time_score(signal.timestamp)
        
        # Final confidence
        final_confidence = base_confidence * multiplier * time_score
        return max(0.1, min(final_confidence, 0.95))
    
    def _calculate_quantity_ratio(self, raw_data: Dict[str, Any]) -> float:
        """Calculate quantity change ratio"""
        bid_change = abs(raw_data.get('bid_qty_change', 0))
        ask_change = abs(raw_data.get('ask_qty_change', 0))
        threshold = raw_data.get('qty_threshold', 1000)
        
        max_change = max(bid_change, ask_change)
        return min(max_change / threshold, 10.0)  # Cap at 10x threshold
    
    def _calculate_price_impact_ratio(self, raw_data: Dict[str, Any]) -> float:
        """Calculate price impact ratio"""
        price_change = abs(raw_data.get('price_change_bps', 0))
        return min(price_change / 100.0, 5.0)  # Normalize and cap
    
    def _calculate_time_score(self, timestamp: datetime) -> float:
        """Calculate time-of-day score"""
        hour = timestamp.hour
        minute = timestamp.minute
        time_decimal = hour + minute / 60.0
        
        # NSE trading hours: 9:15 to 15:30
        if 9.25 <= time_decimal <= 9.75:  # Opening 30 minutes
            return 0.7
        elif 9.75 <= time_decimal <= 11.5:  # Morning
            return 1.0
        elif 11.5 <= time_decimal <= 14.0:  # Midday
            return 1.1
        elif 14.0 <= time_decimal <= 15.25:  # Afternoon
            return 1.0
        else:  # Closing
            return 0.8
    
    def _calculate_volatility_percentile(self, option: OptionsData, market_data: List[OptionsData]) -> float:
        """Calculate volatility percentile"""
        if not option:
            return 0.5
        
        ivs = [opt.implied_volatility for opt in market_data if opt.implied_volatility > 0]
        if not ivs:
            return 0.5
        
        percentile = np.percentile(ivs, 50)  # Median
        return min(option.implied_volatility / percentile, 2.0)
    
    def _calculate_volume_percentile(self, option: OptionsData, market_data: List[OptionsData]) -> float:
        """Calculate volume percentile"""
        if not option:
            return 0.5
        
        volumes = [opt.volume for opt in market_data if opt.volume > 0]
        if not volumes:
            return 0.5
        
        percentile = np.percentile(volumes, 50)  # Median
        return min(option.volume / max(percentile, 1), 5.0)
    
    def _calculate_liquidity_score(self, option: OptionsData) -> float:
        """Calculate liquidity score"""
        if not option:
            return 0.5
        
        # Based on volume and open interest
        volume_score = min(option.volume / 1000, 1.0)
        oi_score = min(option.open_interest / 10000, 1.0)
        
        return (volume_score + oi_score) / 2.0
    
    def _calculate_rsi(self, option: OptionsData, market_data: List[OptionsData]) -> float:
        """Calculate simplified RSI"""
        # Simplified RSI calculation
        return 0.5  # Placeholder - would need price history
    
    def _calculate_bollinger_position(self, option: OptionsData, market_data: List[OptionsData]) -> float:
        """Calculate Bollinger band position"""
        # Simplified calculation
        return 0.5  # Placeholder - would need price history
    
    def _calculate_volume_ma_ratio(self, option: OptionsData, market_data: List[OptionsData]) -> float:
        """Calculate volume to moving average ratio"""
        if not option:
            return 1.0
        
        volumes = [opt.volume for opt in market_data if opt.symbol == option.symbol]
        if len(volumes) < 5:
            return 1.0
        
        ma_volume = np.mean(volumes[-5:])  # 5-period MA
        return option.volume / max(ma_volume, 1)
    
    def _find_signal_option(self, signal: ManipulationSignal, market_data: List[OptionsData]) -> Optional[OptionsData]:
        """Find the option corresponding to the signal"""
        if not signal.symbols_affected:
            return None
        
        symbol_str = signal.symbols_affected[0]
        try:
            parts = symbol_str.split('_')
            if len(parts) >= 3:
                symbol = parts[0]
                strike = float(parts[1])
                option_type = parts[2]
                
                for option in market_data:
                    if (option.symbol == symbol and 
                        option.strike == strike and 
                        option.option_type.value == option_type):
                        return option
        except:
            pass
        
        return None
    
    def _get_default_features(self) -> SignalFeatures:
        """Get default features when data is unavailable"""
        return SignalFeatures(
            pattern_strength=0.5, quantity_ratio=1.0, price_impact_ratio=1.0,
            time_window_ratio=0.5, volatility_percentile=0.5, volume_percentile=0.5,
            time_of_day_score=1.0, liquidity_score=0.5, recent_success_rate=0.5,
            symbol_success_rate=0.5, pattern_frequency=0.1, rsi_level=0.5,
            bollinger_position=0.5, volume_ma_ratio=1.0
        )
    
    def _features_to_vector(self, features: SignalFeatures) -> List[float]:
        """Convert features to vector for ML model"""
        return [
            features.pattern_strength, features.quantity_ratio, features.price_impact_ratio,
            features.time_window_ratio, features.volatility_percentile, features.volume_percentile,
            features.time_of_day_score, features.liquidity_score, features.recent_success_rate,
            features.symbol_success_rate, features.pattern_frequency, features.rsi_level,
            features.bollinger_position, features.volume_ma_ratio
        ]
    
    def _get_feature_names(self) -> List[str]:
        """Get feature names for importance analysis"""
        return [
            'pattern_strength', 'quantity_ratio', 'price_impact_ratio', 'time_window_ratio',
            'volatility_percentile', 'volume_percentile', 'time_of_day_score', 'liquidity_score',
            'recent_success_rate', 'symbol_success_rate', 'pattern_frequency', 'rsi_level',
            'bollinger_position', 'volume_ma_ratio'
        ]
    
    def save_model(self, path: str):
        """Save trained model"""
        if self.is_trained:
            model_data = {
                'model': self.model,
                'scaler': self.scaler,
                'feature_importance': self.feature_importance,
                'is_trained': self.is_trained
            }
            with open(path, 'wb') as f:
                pickle.dump(model_data, f)
            logger.info(f"ML model saved to {path}")
    
    def load_model(self, path: str):
        """Load trained model"""
        try:
            with open(path, 'rb') as f:
                model_data = pickle.load(f)
            
            self.model = model_data['model']
            self.scaler = model_data['scaler']
            self.feature_importance = model_data['feature_importance']
            self.is_trained = model_data['is_trained']
            
            logger.info(f"ML model loaded from {path}")
        except Exception as e:
            logger.error(f"Error loading ML model: {e}")
            self._initialize_model()

# Global ML confidence scorer
ml_confidence_scorer = MLConfidenceScorer()
